<div class="grid">
    <div class="col-12">
        <div class="card">
            <h5>Toolbar</h5>
            <p-toolbar>
                <div class="p-toolbar-group-left flex flex-wrap">
                    <button pButton type="button" label="New" icon="pi pi-plus" class="mr-2"></button>
                    <button pButton type="button" label="Open" icon="pi pi-folder-open" class="p-button-secondary mr-2"></button>

                    <i class="pi pi-bars p-toolbar-separator"></i>

                    <button pButton type="button" icon="pi pi-check" class="p-button-success mr-2"></button>
                    <button pButton type="button" icon="pi pi-trash" class="p-button-warning mr-2"></button>
                    <button pButton type="button" icon="pi pi-print" class="p-button-danger"></button>
                </div>

                <div class="p-toolbar-group-right">
                    <p-splitButton label="Options" icon="pi pi-check" [model]="items"></p-splitButton>
                </div>
            </p-toolbar>
        </div>
    </div>

    <div class="col-12 md:col-6">
        <div class="card">
            <h5>AccordionPanel</h5>
            <p-accordion>
                <p-accordionTab header="Header I" [selected]="true" class="line-height-3 m-0">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore
                    et dolore magna aliqua.
                    Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo
                    consequat.
                    Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                    pariatur.
                    Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id
                    est laborum.
                </p-accordionTab>
                <p-accordionTab header="Header II" class="line-height-3 m-0">
                    Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium,
                    totam rem aperiam, eaque
                    ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo
                    enim ipsam voluptatem quia
                    voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione
                    voluptatem sequi nesciunt.
                    Consectetur, adipisci velit, sed quia non numquam eius modi.
                </p-accordionTab>
                <p-accordionTab header="Header III" class="line-height-3 m-0">
                    At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum
                    deleniti atque corrupti quos dolores
                    et quas molestias excepturi sint occaecati cupiditate non provident, similique sunt in culpa qui
                    officia deserunt mollitia animi, id est laborum et dolorum fuga.
                    Et harum quidem rerum facilis est et expedita distinctio. Nam libero tempore, cum soluta nobis est
                    eligendi optio cumque nihil impedit
                    quo minus.
                </p-accordionTab>
            </p-accordion>
        </div>

        <div class="card">
            <h5>TabView</h5>
            <p-tabView orientation="left">
                <p-tabPanel header="Header I" class="line-height-3 m-0">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore
                    et dolore magna aliqua.
                    Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo
                    consequat.
                    Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                    pariatur.
                    Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id
                    est laborum.
                </p-tabPanel>
                <p-tabPanel header="Header II" class="line-height-3 m-0">
                    Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium,
                    totam rem aperiam, eaque
                    ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo
                    enim ipsam voluptatem quia
                    voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione
                    voluptatem sequi nesciunt.
                    Consectetur, adipisci velit, sed quia non numquam eius modi.
                </p-tabPanel>
                <p-tabPanel header="Header III" class="line-height-3 m-0">
                    At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum
                    deleniti atque corrupti quos dolores
                    et quas molestias excepturi sint occaecati cupiditate non provident, similique sunt in culpa qui
                    officia deserunt mollitia animi, id est laborum et dolorum fuga.
                    Et harum quidem rerum facilis est et expedita distinctio. Nam libero tempore, cum soluta nobis est
                    eligendi optio cumque nihil impedit
                    quo minus.
                </p-tabPanel>
            </p-tabView>
        </div>
    </div>

    <div class="col-12 md:col-6">
        <div class="card">
            <h5>Panel</h5>
            <p-panel header="Header" [toggleable]="true" class="line-height-3 m-0">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
                dolore magna aliqua.
                Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo
                consequat.
                Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
                Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est
                laborum.
            </p-panel>
        </div>

        <div class="card">
            <h5>Fieldset</h5>
            <p-fieldset legend="Legend" [toggleable]="true" class="line-height-3 m-0">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
                dolore magna aliqua.
                Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo
                consequat.
                Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
                Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est
                laborum.
            </p-fieldset>
        </div>

        <div class="card">
            <div class="flex align-items-center justify-content-between mb-0">
                <h5>Card</h5>
                <p-menu #menu [popup]="true" [model]="cardMenu"></p-menu>
                <button pButton type="button" icon="pi pi-plus" class="p-button-text"
                        (click)="menu.toggle($event)"></button>
            </div>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
                dolore magna aliqua.
                Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo
                consequat.
                Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
                Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est
                laborum.</p>
        </div>
    </div>

    <div class="col-12">
        <div class="card">
            <h5>Divider</h5>
            <div class="grid">
                <div class="col-5 flex align-items-center justify-content-center">
                    <div class="p-fluid">
                        <div class="field">
                            <label for="username">Username</label>
                            <input pInputText id="username" type="text"/>
                        </div>
                        <div class="field">
                            <label for="password">Password</label>
                            <input pInputText id="password" type="password"/>
                        </div>
                        <p-button label="Login" class="mt-2"></p-button>
                    </div>
                </div>
                <div class="col-2">
                    <p-divider layout="vertical">
                        <b>OR</b>
                    </p-divider>
                </div>
                <div class="col-5 align-items-center justify-content-center">
                    <p class="line-height-3 m-0">Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium,
                        totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi
                        architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit
                        aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione
                        voluptatem sequi nesciunt. Consectetur, adipisci velit, sed quia non numquam eius modi.</p>

                    <p-divider layout="horizontal" align="center">
                        <span class="p-tag">Badge</span>
                    </p-divider>

                    <p class="line-height-3 m-0">At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum
                        deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati
                        cupiditate non provident, similique sunt in culpa qui officia deserunt mollitia animi, id est
                        laborum et dolorum fuga. Et harum quidem rerum facilis est et expedita distinctio.
                        Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus.</p>

                    <p-divider align="right">
                        <p-button label="Button" icon="pi pi-search" styleClass="p-button-outlined"></p-button>
                    </p-divider>

                    <p class="line-height-3 m-0">Temporibus autem quibusdam et aut officiis debitis aut rerum necessitatibus saepe eveniet ut et
                        voluptates repudiandae sint et molestiae non recusandae. Itaque earum rerum hic tenetur
                        a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis
                        doloribus asperiores repellat.
                        Donec vel volutpat ipsum. Integer nunc magna, posuere ut tincidunt eget, egestas vitae sapien.
                        Morbi dapibus luctus odio.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12">
        <div class="card">
            <h5>Splitter</h5>
            <p-splitter [style]="{'height': '300px'}" [panelSizes]="[5,0]" styleClass="mb-5" [panelStyle]="{'overflow': 'scroll'}">
                <ng-template pTemplate>
                    <p class="col m-3">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut
                        labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation
                        ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit
                        in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
                        Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim
                        id est laborum.
                    </p>
                </ng-template>
                <ng-template pTemplate>
                    <p-splitter layout="vertical" [panelSizes]="[30,70]" [minSizes]="[10,10]" [style]="{'overflow':'scroll'}">
                        <ng-template pTemplate>
                            <p class="col m-3">
                                Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium
                                doloremque
                                laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi
                                architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia
                                voluptas
                                sit
                                aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione
                                voluptatem sequi nesciunt. Consectetur, adipisci velit, sed quia non numquam eius
                                modi.
                            </p>
                        </ng-template>
                        <ng-template pTemplate>
                            <p class="col m-3">
                                At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis
                                praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias
                                excepturi sint occaecati cupiditate non provident, similique sunt in culpa qui
                                officia deserunt mollitia animi, id est laborum et dolorum fuga. Et harum quidem
                                rerum facilis est et expedita distinctio. Nam libero tempore, cum soluta nobis est
                                eligendi optio cumque nihil impedit quo minus.
                            </p>
                        </ng-template>
                    </p-splitter>
                </ng-template>
            </p-splitter>
        </div>
    </div>
</div>
