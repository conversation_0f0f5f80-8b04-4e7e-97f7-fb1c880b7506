<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/



// CRUD utilisateurs (employés et formateurs)
Route::apiResource('users', App\Http\Controllers\UserController::class);

// CRUD formations
Route::apiResource('formations', App\Http\Controllers\FormationController::class);

// Gestion des participants aux formations
Route::get('formations/{id}/participants', [App\Http\Controllers\FormationController::class, 'getParticipants']);
Route::put('formations/{formationId}/participants/{participantId}', [App\Http\Controllers\FormationController::class, 'updateParticipantStatus']);

// CRUD équipes
Route::apiResource('teams', App\Http\Controllers\TeamController::class);

// Statistiques
Route::prefix('statistics')->group(function () {
    Route::get('dashboard', [App\Http\Controllers\StatisticsController::class, 'dashboard']);
    Route::get('formations', [App\Http\Controllers\StatisticsController::class, 'formations']);
    Route::get('teams', [App\Http\Controllers\StatisticsController::class, 'teams']);
    Route::get('employees', [App\Http\Controllers\StatisticsController::class, 'employees']);
    Route::get('monthly', [App\Http\Controllers\StatisticsController::class, 'monthly']);
});

// Affecter un employé à une équipe
Route::put('users/{user}/team', [App\Http\Controllers\UserController::class, 'updateTeam']);
